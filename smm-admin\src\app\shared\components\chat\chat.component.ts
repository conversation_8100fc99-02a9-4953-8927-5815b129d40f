import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { ChatService, ChatRoom, ChatMessage, ChatUser, ChatMessageRequest } from '../../../core/services/chat.service';
import { UserService } from '../../../core/services/user.service';
import { UserRes } from '../../../model/response/user-res.model';
import { Subscription } from 'rxjs';

import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';

@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss'],
  standalone: true,
  imports: [ CommonModule, TranslateModule, FormsModule, IconsModule ]
})
export class ChatComponent implements OnI<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;

  // Support chat properties
  supportMessages: ChatMessage[] = [];
  newMessage: string = '';
  isLoading = false;
  isChatOpen = false;
  currentUser: UserRes | undefined;
  supportChatRoom: ChatRoom | null = null;

  private subscriptions: Subscription[] = [];

  constructor(
    private chatService: ChatService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    this.initializeSupportChat();
    this.subscribeToSupportMessages();
    this.loadInitialSupportMessages();
  }

  private loadCurrentUser(): void {
    const userSub = this.userService.user$.subscribe(user => {
      this.currentUser = user;
    });
    this.subscriptions.push(userSub);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.chatService.disconnect();
  }

  private subscribeToSupportMessages(): void {
    // Subscribe to support messages observable for real-time updates
    const supportMessagesSub = this.chatService.supportMessages$.subscribe(messages => {
      console.log('Chat component received support messages update:', messages.length);
      this.supportMessages = messages;
      this.scrollToBottom();
    });
    this.subscriptions.push(supportMessagesSub);
  }

  private loadInitialSupportMessages(): void {
    // Load initial support messages from API
    this.chatService.loadSupportMessages().subscribe({
      next: (response) => {
        console.log('Initial support messages loaded:', response);
      },
      error: (error) => {
        console.error('Error loading initial support messages:', error);
      }
    });
  }

  private initializeSupportChat(): void {
    // For panel users, get or create their support chat room
    this.chatService.getOrCreateSupportChatRoom().subscribe({
      next: (response) => {
        console.log('Support chat room response:', response);
        this.supportChatRoom = response;
        this.loadSupportMessages();
      },
      error: (error) => {
        console.error('Error getting support chat room:', error);
        // Fallback to loading messages without room
        this.loadSupportMessages();
      }
    });
  }

  loadSupportMessages(): void {
    // This method is now mainly for manual refresh if needed
    // Real-time updates are handled by subscribeToSupportMessages()
    if (!this.currentUser) return;

    this.isLoading = true;
    this.chatService.loadSupportMessages().subscribe({
      next: (response) => {
        console.log('Support messages refreshed:', response);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error refreshing support messages:', error);
        this.isLoading = false;
      }
    });
  }

  sendSupportMessage(): void {
    if (!this.newMessage.trim()) {
      return;
    }

    console.log('=== Sending Support Message ===');
    console.log('Message content:', this.newMessage.trim());
    console.log('Support chat room:', this.supportChatRoom);
    console.log('Current user:', this.currentUser);

    this.isLoading = true;
    const messageRequest: ChatMessageRequest = {
      chatRoomId: this.supportChatRoom?.id || 0, // Use chat room ID if available
      content: this.newMessage.trim(),
      messageType: 'TEXT'
    };

    console.log('Message request:', messageRequest);

    const sub = this.chatService.sendSupportMessage(messageRequest).subscribe({
      next: (response) => {
        console.log('Support message sent successfully:', response);
        this.newMessage = '';
        // No need to reload messages - WebSocket will handle real-time updates
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error sending support message:', error);
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  toggleChat(): void {
    this.isChatOpen = !this.isChatOpen;
    if (this.isChatOpen) {
      this.loadSupportMessages();
    }
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendSupportMessage();
    }
  }

  private scrollToBottom(): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 100);
  }

  getRoomDisplayName(room: ChatRoom): string {
    if (room.type === 'DIRECT' && room.participants) {
      // For direct chats, show the other participant's name
      const otherParticipant = room.participants.find(p => p.user_id !== this.getCurrentUserId());
      return otherParticipant?.user?.user_name || room.name || 'Direct Chat';
    }
    return room.name || 'Group Chat';
  }

  getCurrentUserId(): number {
    return this.currentUser?.id || 0;
  }

  formatMessageTime(timestamp: string): string {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  getTotalUnreadCount(): number {
    // For support chat, we can return 0 for now
    // Later we can implement unread count for support messages
    return 0;
  }
}
