import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { AdminPanelService } from '../../core/services/admin-panel.service';
import { AdminPanelTenant } from '../../model/response/admin-panel-tenant.model';
import { AdminPanelUser } from '../../model/response/admin-panel-user.model';
import { AdminPanelUserRequest } from '../../model/request/admin-panel-user.model';
import { ToastService } from '../../core/services/toast.service';
import { CurrencyService } from '../../core/services/currency.service';
import { ChatService, ChatRoom } from '../../core/services/chat.service';
import { Subscription } from 'rxjs';
import { DomainInfoComponent } from '../popup/domain-info/domain-info.component';
import { OrderTrackingComponent } from './order-tracking/order-tracking.component';
import { SupportChatComponent } from './support-chat/support-chat.component';

@Component({
  selector: 'app-admin-panel',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule,
    DomainInfoComponent,
    OrderTrackingComponent,
    SupportChatComponent
  ],
  templateUrl: './admin-panel.component.html',
  styleUrls: ['./admin-panel.component.css']
})
export class AdminPanelComponent implements OnInit, OnDestroy {
  // Make Math available in template
  Math = Math;

  // Tab management
  activeTab: 'panels' | 'users' | 'orders' | 'messages' = 'panels';

  // Panels data
  panels: AdminPanelTenant[] = [];
  panelsSearchTerm: string = '';
  panelsPagination = {
    page_number: 0,
    page_size: 10,
    total_elements: 0,
    total_pages: 0
  };

  // Users data
  users: AdminPanelUser[] = [];
  usersSearchTerm: string = '';
  usersPagination = {
    page_number: 0,
    page_size: 10,
    total_elements: 0,
    total_pages: 0
  };

  // Loading state
  isLoading = false;

  // Support messages data
  supportMessages: any[] = [];
  supportChatRooms: ChatRoom[] = [];
  selectedChatRoom: ChatRoom | null = null;
  showChatInterface = false;

  // Add money modal
  showAddMoneyModal = false;
  selectedUser: AdminPanelUser | null = null;
  addMoneyForm = {
    amount: '',
    note: ''
  };

  // Domain info modal
  showDomainInfoModal = false;
  selectedTenantId = '';
  selectedDomain = '';

  private subscriptions: Subscription[] = [];

  constructor(
    private adminPanelService: AdminPanelService,
    private toastService: ToastService,
    private currencyService: CurrencyService,
    private chatService: ChatService
  ) {}

  ngOnInit() {
    // Subscribe to panels data
    this.subscriptions.push(
      this.adminPanelService.panels$.subscribe(panels => {
        this.panels = panels;
      })
    );

    // Subscribe to users data
    this.subscriptions.push(
      this.adminPanelService.users$.subscribe(users => {
        this.users = users;
      })
    );

    // Subscribe to loading state
    this.subscriptions.push(
      this.adminPanelService.loading$.subscribe(loading => {
        this.isLoading = loading;
      })
    );

    // Subscribe to panels pagination
    this.subscriptions.push(
      this.adminPanelService.panelsPagination$.subscribe(pagination => {
        this.panelsPagination = pagination;
      })
    );

    // Subscribe to users pagination
    this.subscriptions.push(
      this.adminPanelService.usersPagination$.subscribe(pagination => {
        this.usersPagination = pagination;
      })
    );

    // Load initial data
    this.loadPanels();

    // Subscribe to WebSocket messages for real-time chat updates
    this.subscribeToSupportMessages();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Tab management
  switchTab(tab: 'panels' | 'users' | 'orders' | 'messages') {
    this.activeTab = tab;
    if (tab === 'panels') {
      this.loadPanels();
    } else if (tab === 'users') {
      this.loadUsers();
    } else if (tab === 'messages') {
      this.loadSupportMessages();
    }
    // Orders tab doesn't need initial loading as it's handled by the component
  }

  // Panels methods
  loadPanels(page: number = 0) {
    this.adminPanelService.getAllPanels(page, this.panelsPagination.page_size, this.panelsSearchTerm)
      .subscribe({
        error: (error) => {
          console.error('Error loading panels:', error);
          this.toastService.showError('Failed to load panels');
        }
      });
  }

  searchPanels() {
    this.loadPanels(0);
  }

  resetPanelsSearch() {
    this.panelsSearchTerm = '';
    this.loadPanels(0);
  }

  // Users methods
  loadUsers(page: number = 0) {
    this.adminPanelService.getMainTenantUsers(page, this.usersPagination.page_size, this.usersSearchTerm)
      .subscribe({
        error: (error) => {
          console.error('Error loading users:', error);
          this.toastService.showError('Failed to load users');
        }
      });
  }

  searchUsers() {
    this.loadUsers(0);
  }

  resetUsersSearch() {
    this.usersSearchTerm = '';
    this.loadUsers(0);
  }

  // Add money functionality
  openAddMoneyModal(user: AdminPanelUser) {
    this.selectedUser = user;
    this.addMoneyForm = {
      amount: '',
      note: ''
    };
    this.showAddMoneyModal = true;
  }

  closeAddMoneyModal() {
    this.showAddMoneyModal = false;
    this.selectedUser = null;
    this.addMoneyForm = {
      amount: '',
      note: ''
    };
  }

  // Domain info modal methods
  openDomainInfoModal(tenantId: string, domain: string) {
    this.selectedTenantId = tenantId;
    this.selectedDomain = domain;
    this.showDomainInfoModal = true;
  }

  closeDomainInfoModal() {
    this.showDomainInfoModal = false;
    this.selectedTenantId = '';
    this.selectedDomain = '';
  }

  addMoney() {
    if (!this.selectedUser || !this.addMoneyForm.amount) {
      this.toastService.showError('Please enter a valid amount');
      return;
    }

    const amount = parseFloat(this.addMoneyForm.amount);
    if (isNaN(amount) || amount <= 0) {
      this.toastService.showError('Please enter a valid positive amount');
      return;
    }

    const request: AdminPanelUserRequest = {
      user_id: this.selectedUser.id,
      amount: amount,
      source: 'BONUS',
      note: this.addMoneyForm.note || `Added by admin panel for user ${this.selectedUser.user_name}`
    };

    this.adminPanelService.addMoneyToUser(request).subscribe({
      next: (_) => {
        this.toastService.showSuccess(`Successfully added $${amount} to ${this.selectedUser!.user_name}`);
        this.closeAddMoneyModal();
        // Refresh users list
        this.loadUsers(this.usersPagination.page_number);
      },
      error: (error) => {
        console.error('Error adding money:', error);
        this.toastService.showError('Failed to add money to user');
      }
    });
  }

  // Pagination methods
  loadPanelsPage(page: number) {
    this.loadPanels(page);
  }

  loadUsersPage(page: number) {
    this.loadUsers(page);
  }

  // Utility methods
  formatBalance(balance: number | undefined): string {
    return '$' + this.currencyService.formatBalance(balance);
  }

  formatDate(dateString: string): string {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  }

  getStatusClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'activated':
        return 'bg-green-100 text-green-800';
      case 'suspended':
      case 'deactivated':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Support Messages methods
  private subscribeToSupportMessages(): void {
    // Subscribe to WebSocket messages for real-time updates
    const messagesSub = this.chatService.messages$.subscribe(_ => {
      // Refresh chat rooms when new messages arrive
      if (this.activeTab === 'messages' && !this.showChatInterface) {
        this.loadSupportMessages();
      }
    });
    this.subscriptions.push(messagesSub);
  }

  loadSupportMessages() {
    this.isLoading = true;
    this.chatService.getSupportChatRooms().subscribe({
      next: (response) => {
        console.log('Admin panel - Support chat rooms response:', response);
        if (response.content) {
          this.supportChatRooms = response.content;
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading support chat rooms:', error);
        this.toastService.showError('Failed to load support chat rooms');
        this.isLoading = false;
      }
    });
  }

  openChatRoom(chatRoom: ChatRoom) {
    this.selectedChatRoom = chatRoom;
    this.showChatInterface = true;
  }

  onBackToRooms() {
    this.showChatInterface = false;
    this.selectedChatRoom = null;
    // Refresh the chat rooms list
    this.loadSupportMessages();
  }

  getUserDisplayName(chatRoom: ChatRoom): string {
    if (!chatRoom.creator) return 'Unknown User';
    return chatRoom.creator.full_name ||
           chatRoom.creator.user_name ||
           'Unknown User';
  }

  getUserEmail(chatRoom: ChatRoom): string {
    if (!chatRoom.creator) return '';
    return chatRoom.creator.email || '';
  }

  getLastMessagePreview(chatRoom: ChatRoom): string {
    if (!chatRoom.last_message) return 'No messages yet';
    const content = chatRoom.last_message.content;
    return content.length > 50 ? content.substring(0, 50) + '...' : content;
  }

  getLastMessageTime(chatRoom: ChatRoom): string {
    if (!chatRoom.last_message) return '';
    return this.formatMessageTime(chatRoom.last_message.created_at || chatRoom.last_message.createdAt || '');
  }

  formatMessageTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }
  }
}
