<div class="admin-panel-container p-6">
  <!-- Header -->
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">Admin Panel Management</h1>
    <p class="text-gray-600">Manage all panels and main tenant users</p>
  </div>

  <!-- Tab Navigation -->
  <div class="mb-6">
    <nav class="flex space-x-8" aria-label="Tabs">
      <button
        (click)="switchTab('panels')"
        [class]="activeTab === 'panels' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
        <fa-icon [icon]="['fas', 'server']" class="mr-2"></fa-icon>
        {{ 'admin.panel_management' | translate }}
      </button>
      <button
        (click)="switchTab('users')"
        [class]="activeTab === 'users' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
        <fa-icon [icon]="['fas', 'users']" class="mr-2"></fa-icon>
        {{ 'admin.user_management' | translate }}
      </button>
      <button
        (click)="switchTab('orders')"
        [class]="activeTab === 'orders' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
        <fa-icon [icon]="['fas', 'shopping-cart']" class="mr-2"></fa-icon>
        {{ 'admin.order_tracking.title' | translate }}
      </button>
      <button
        (click)="switchTab('messages')"
        [class]="activeTab === 'messages' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
        <fa-icon [icon]="['fas', 'comments']" class="mr-2"></fa-icon>
        {{ 'admin.messages' | translate }}
      </button>
    </nav>
  </div>

  <!-- Panels Tab -->
  <div *ngIf="activeTab === 'panels'" class="panels-tab">
    <!-- Search and Filters -->
    <div class="mb-4 flex flex-col sm:flex-row gap-4">
      <div class="flex-1">
        <div class="relative">
          <input
            type="text"
            [(ngModel)]="panelsSearchTerm"
            (keyup.enter)="searchPanels()"
            placeholder="Search by domain..."
            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
          <fa-icon [icon]="['fas', 'search']" class="absolute left-3 top-3 text-gray-400"></fa-icon>
        </div>
      </div>
      <div class="flex gap-2">
        <button
          (click)="searchPanels()"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          <fa-icon [icon]="['fas', 'search']" class="mr-2"></fa-icon>
          Search
        </button>
        <button
          (click)="resetPanelsSearch()"
          class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
          <fa-icon [icon]="['fas', 'times']" class="mr-2"></fa-icon>
          Reset
        </button>
      </div>
    </div>

    <!-- Panels Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Domain</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Email</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subscription</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngFor="let panel of panels" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm  font-medium text-blue-500 cursor-pointer hover:text-blue-600 transition-colors"
                     (click)="openDomainInfoModal(panel.id, panel.domain)">
                  {{ panel.domain }}
                </div>
                <div class="text-sm text-gray-500">{{ panel.id }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span [class]="getStatusClass(panel.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ panel.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ panel.contact_email || '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div *ngIf="panel.subscription_end_date">
                  Expires: {{ formatDate(panel.subscription_end_date) }}
                </div>
                <div *ngIf="!panel.subscription_end_date" class="text-gray-500">-</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(panel.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ panel.total_users || 0 }}
              </td>
            </tr>
            <tr *ngIf="panels.length === 0 && !isLoading">
              <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                No panels found
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Panels Pagination -->
      <div *ngIf="panelsPagination.total_pages > 1" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button
            (click)="loadPanelsPage(panelsPagination.page_number - 1)"
            [disabled]="panelsPagination.page_number === 0"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
            Previous
          </button>
          <button
            (click)="loadPanelsPage(panelsPagination.page_number + 1)"
            [disabled]="panelsPagination.page_number >= panelsPagination.total_pages - 1"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
            Next
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              Showing {{ panelsPagination.page_number * panelsPagination.page_size + 1 }} to 
              {{ Math.min((panelsPagination.page_number + 1) * panelsPagination.page_size, panelsPagination.total_elements) }} of 
              {{ panelsPagination.total_elements }} results
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                (click)="loadPanelsPage(panelsPagination.page_number - 1)"
                [disabled]="panelsPagination.page_number === 0"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                <fa-icon [icon]="['fas', 'chevron-left']"></fa-icon>
              </button>
              <button
                (click)="loadPanelsPage(panelsPagination.page_number + 1)"
                [disabled]="panelsPagination.page_number >= panelsPagination.total_pages - 1"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                <fa-icon [icon]="['fas', 'chevron-right']"></fa-icon>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Users Tab -->
  <div *ngIf="activeTab === 'users'" class="users-tab">
    <!-- Search and Filters -->
    <div class="mb-4 flex flex-col sm:flex-row gap-4">
      <div class="flex-1">
        <div class="relative">
          <input
            type="text"
            [(ngModel)]="usersSearchTerm"
            (keyup.enter)="searchUsers()"
            placeholder="Search by username, email, or phone..."
            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
          <fa-icon [icon]="['fas', 'search']" class="absolute left-3 top-3 text-gray-400"></fa-icon>
        </div>
      </div>
      <div class="flex gap-2">
        <button
          (click)="searchUsers()"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          <fa-icon [icon]="['fas', 'search']" class="mr-2"></fa-icon>
          Search
        </button>
        <button
          (click)="resetUsersSearch()"
          class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
          <fa-icon [icon]="['fas', 'times']" class="mr-2"></fa-icon>
          Reset
        </button>
      </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Balance</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngFor="let user of users" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ user.user_name }}</div>
                <div class="text-sm text-gray-500">ID: {{ user.id }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ user.email }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="font-medium">{{ formatBalance(user.balance) }}</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span [class]="getStatusClass(user.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ user.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ user.last_login_at ? formatDate(user.last_login_at) : 'Never' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button
                  (click)="openAddMoneyModal(user)"
                  class="text-blue-600 hover:text-blue-900 mr-3">
                  <fa-icon [icon]="['fas', 'plus-circle']" class="mr-1"></fa-icon>
                  Add Money
                </button>
              </td>
            </tr>
            <tr *ngIf="users.length === 0 && !isLoading">
              <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                No users found
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Users Pagination -->
      <div *ngIf="usersPagination.total_pages > 1" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button
            (click)="loadUsersPage(usersPagination.page_number - 1)"
            [disabled]="usersPagination.page_number === 0"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
            Previous
          </button>
          <button
            (click)="loadUsersPage(usersPagination.page_number + 1)"
            [disabled]="usersPagination.page_number >= usersPagination.total_pages - 1"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
            Next
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              Showing {{ usersPagination.page_number * usersPagination.page_size + 1 }} to 
              {{ Math.min((usersPagination.page_number + 1) * usersPagination.page_size, usersPagination.total_elements) }} of 
              {{ usersPagination.total_elements }} results
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                (click)="loadUsersPage(usersPagination.page_number - 1)"
                [disabled]="usersPagination.page_number === 0"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                <fa-icon [icon]="['fas', 'chevron-left']"></fa-icon>
              </button>
              <button
                (click)="loadUsersPage(usersPagination.page_number + 1)"
                [disabled]="usersPagination.page_number >= usersPagination.total_pages - 1"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                <fa-icon [icon]="['fas', 'chevron-right']"></fa-icon>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div *ngIf="isLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      <span class="text-gray-700">Loading...</span>
    </div>
  </div>

  <!-- Orders Tab -->
  <div *ngIf="activeTab === 'orders'" class="orders-tab">
    <app-order-tracking></app-order-tracking>
  </div>

  <!-- Messages Tab -->
  <div *ngIf="activeTab === 'messages'" class="messages-tab">
    <!-- Chat Rooms List -->
    <div *ngIf="!showChatInterface" class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Support Chat Rooms</h3>
        <p class="text-sm text-gray-600 mt-1">Chat conversations with panel users</p>
      </div>

      <!-- Chat Rooms List -->
      <div class="divide-y divide-gray-200 max-h-96 overflow-y-auto">
        <div *ngFor="let chatRoom of supportChatRooms"
             class="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
             (click)="openChatRoom(chatRoom)">
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <fa-icon [icon]="['fas', 'user']" class="text-blue-600"></fa-icon>
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-900">
                    {{ getUserDisplayName(chatRoom) }}
                  </p>
                  <p class="text-sm text-gray-500">
                    {{ getUserEmail(chatRoom) }}
                  </p>
                </div>
                <div class="text-sm text-gray-500">
                  {{ getLastMessageTime(chatRoom) }}
                </div>
              </div>
              <div class="mt-2">
                <p class="text-sm text-gray-600">{{ getLastMessagePreview(chatRoom) }}</p>
              </div>
              <div class="mt-2 flex items-center">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <fa-icon [icon]="['fas', 'comments']" class="mr-1"></fa-icon>
                  Chat Room
                </span>
              </div>
            </div>
            <div class="flex-shrink-0">
              <fa-icon [icon]="['fas', 'chevron-right']" class="text-gray-400"></fa-icon>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="supportChatRooms.length === 0 && !isLoading" class="p-6 text-center">
          <fa-icon [icon]="['fas', 'comments']" class="text-gray-400 text-4xl mb-4"></fa-icon>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No chat rooms yet</h3>
          <p class="text-gray-600">Support chat rooms from panel users will appear here.</p>
        </div>

        <!-- Loading State -->
        <div *ngIf="isLoading" class="p-6 text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p class="text-gray-600 mt-2">Loading chat rooms...</p>
        </div>
      </div>

      <!-- Refresh Button -->
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <button
          (click)="loadSupportMessages()"
          [disabled]="isLoading"
          class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors">
          <fa-icon [icon]="['fas', 'refresh']" class="mr-2" [spin]="isLoading"></fa-icon>
          Refresh Chat Rooms
        </button>
      </div>
    </div>

    <!-- Chat Interface -->
    <div *ngIf="showChatInterface && selectedChatRoom">
      <app-support-chat
        [selectedChatRoom]="selectedChatRoom"
        (backToRooms)="onBackToRooms()">
      </app-support-chat>
    </div>
  </div>

</div>

<!-- Domain Info Modal -->
<app-domain-info
  *ngIf="showDomainInfoModal"
  [tenantId]="selectedTenantId"
  [domain]="selectedDomain"
  (close)="closeDomainInfoModal()">
</app-domain-info>

<!-- Add Money Modal -->
<div *ngIf="showAddMoneyModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium text-gray-900">Add Money to User</h3>
      <button (click)="closeAddMoneyModal()" class="text-gray-400 hover:text-gray-600">
        <fa-icon [icon]="['fas', 'times']"></fa-icon>
      </button>
    </div>

    <div class="mb-4">
      <p class="text-sm text-gray-600 mb-2">
        Adding money to: <strong>{{ selectedUser?.user_name }}</strong>
      </p>
      <p class="text-sm text-gray-600">
        Current balance: <strong>{{ formatBalance(selectedUser?.balance) }}</strong>
      </p>
    </div>

    <form (ngSubmit)="addMoney()" class="space-y-4">
      <div>
        <label for="amount" class="block text-sm font-medium text-gray-700 mb-1">Amount ($)</label>
        <input
          type="number"
          id="amount"
          [(ngModel)]="addMoneyForm.amount"
          name="amount"
          step="0.01"
          min="0.01"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter amount">
      </div>

      <div>
        <label for="note" class="block text-sm font-medium text-gray-700 mb-1">Note (optional)</label>
        <textarea
          id="note"
          [(ngModel)]="addMoneyForm.note"
          name="note"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter a note for this transaction"></textarea>
      </div>

      <div class="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          (click)="closeAddMoneyModal()"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
          Cancel
        </button>
        <button
          type="submit"
          [disabled]="!addMoneyForm.amount || isLoading"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
          <fa-icon [icon]="['fas', 'plus']" class="mr-2"></fa-icon>
          Add Money
        </button>
      </div>
    </form>
  </div>
</div>
