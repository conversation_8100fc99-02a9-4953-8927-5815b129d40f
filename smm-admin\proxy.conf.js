/**
 * Angular CLI proxy configuration with additional options
 * This file provides more flexibility than the JSON version
 */

module.exports = {
  '/api': {
    target: 'https://autovnfb.com',
    secure: false,
    changeOrigin: true,
    logLevel: 'debug',
    pathRewrite: {
      '^/api': '/api/v1'
    },
    headers: {
      'X-Tenant-ID': '0e22c37d-bfb5-4276-bd30-355fcdb39c9e',
      'X-Tenant-Domain': 'autovnfb.dev'
    },
    // Bypass the proxy for certain requests
    bypass: function(req, res, proxyOptions) {
      // You can add logic here to bypass the proxy for certain requests if needed
      if (req.headers.accept && req.headers.accept.indexOf('html') !== -1) {
        console.log('Skipping proxy for browser request.');
        return '/index.html';
      }
    },
    // Handle proxy errors
    onError: function(err, req, res) {
      console.error('Proxy error:', err);
      res.writeHead(500, {
        'Content-Type': 'text/plain'
      });
      res.end('Proxy error: ' + err);
    },
    // Log proxy activity
    onProxyRes: function(proxyRes, req, res) {
      console.log('Response from backend:', proxyRes.statusCode, req.url);
    }
  },

  // WebSocket proxy configuration
  '/ws': {
    target: 'https://autovnfb.com',
    secure: false,
    changeOrigin: true,
    logLevel: 'debug',
    pathRewrite: {
      '^/ws': '/api/v1/ws'
    },
    headers: {
      'X-Tenant-ID': '0e22c37d-bfb5-4276-bd30-355fcdb39c9e',
      'X-Tenant-Domain': 'autovnfb.dev'
    },
    ws: true, // Enable WebSocket proxying
    onError: function(err, req, res) {
      console.error('WebSocket proxy error:', err);
    },
    onProxyRes: function(proxyRes, req, res) {
      console.log('WebSocket response from backend:', proxyRes.statusCode, req.url);
    }
  },

  // Add additional proxy configurations if needed
  // For example, if you have a separate CDN endpoint:
  '/cdn': {
    target: 'http://localhost',
    secure: false,
    changeOrigin: true,
    logLevel: 'debug'
  }
};
