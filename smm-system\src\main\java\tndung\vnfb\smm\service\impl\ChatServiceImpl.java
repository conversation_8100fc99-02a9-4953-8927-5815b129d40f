package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.constant.enums.ChatRoomType;
import tndung.vnfb.smm.constant.enums.Role;
import tndung.vnfb.smm.dto.chat.*;
import tndung.vnfb.smm.entity.*;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.mapper.ChatMessageMapper;
import tndung.vnfb.smm.mapper.ChatParticipantMapper;
import tndung.vnfb.smm.mapper.ChatRoomMapper;
import tndung.vnfb.smm.mapper.GUserMapper;
import tndung.vnfb.smm.repository.nontenant.ChatMessageRepository;
import tndung.vnfb.smm.repository.nontenant.ChatParticipantRepository;
import tndung.vnfb.smm.repository.nontenant.ChatRoomRepository;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.service.ChatService;
import tndung.vnfb.smm.service.AuthenticationFacade;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ChatServiceImpl implements ChatService {

    private final ChatRoomRepository chatRoomRepository;
    private final ChatMessageRepository chatMessageRepository;
    private final ChatParticipantRepository chatParticipantRepository;
    private final GUserRepository userRepository;
    private final AuthenticationFacade authenticationFacade;
    
    private final ChatRoomMapper chatRoomMapper;
    private final ChatMessageMapper chatMessageMapper;
    private final ChatParticipantMapper chatParticipantMapper;
    private final GUserMapper gUserMapper;

    private final SimpMessagingTemplate messagingTemplate;

    private GUser getCurrentUser() {
        return authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));
    }

    @Override
    public ChatRoomRes createChatRoom(ChatRoomReq req) {
        GUser currentUser = getCurrentUser();

        // Validate participants
        if (req.getParticipantUserIds() == null || req.getParticipantUserIds().isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.INVALID_REQUEST);
        }
        
        // For direct chat, check if room already exists
        if (req.getType() == ChatRoomType.DIRECT && req.getParticipantUserIds().size() == 1) {
            Long otherUserId = req.getParticipantUserIds().get(0);
            var existingRoom = chatRoomRepository.findDirectChatRoom(currentUser.getId(), otherUserId);
            if (existingRoom.isPresent()) {
                return getChatRoomWithDetails(existingRoom.get());
            }
        }
        
        // Create new chat room
        ChatRoom chatRoom = chatRoomMapper.toEntity(req);
        chatRoom.setCreatedBy(currentUser.getId());
        chatRoom = chatRoomRepository.save(chatRoom);
        
        // Add participants
        List<ChatParticipant> participants = new ArrayList<>();
        
        // Add current user as participant
        participants.add(createParticipant(chatRoom.getId(), currentUser.getId()));
        
        // Add other participants
        for (Long userId : req.getParticipantUserIds()) {
            if (!userId.equals(currentUser.getId())) {
                participants.add(createParticipant(chatRoom.getId(), userId));
            }
        }
        
        chatParticipantRepository.saveAll(participants);
        
        return getChatRoomWithDetails(chatRoom);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ChatRoomRes> getChatRooms(Pageable pageable) {
        GUser currentUser = getCurrentUser();
        Page<ChatRoom> chatRooms = chatRoomRepository.findChatRoomsByUserId(currentUser.getId(), pageable);

        List<ChatRoomRes> chatRoomResList = chatRooms.getContent().stream()
                .map(this::getChatRoomWithDetails)
                .collect(Collectors.toList());

        return new PageImpl<>(chatRoomResList, pageable, chatRooms.getTotalElements());
    }

    @Override
    public ChatRoomRes getOrCreateDirectChat(Long otherUserId) {
        GUser currentUser = getCurrentUser();

        // Validate other user exists
        GUser otherUser = userRepository.findById(otherUserId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        // Check if direct chat already exists
        var existingRoom = chatRoomRepository.findDirectChatRoom(currentUser.getId(), otherUserId);
        if (existingRoom.isPresent()) {
            return getChatRoomWithDetails(existingRoom.get());
        }

        // Create new direct chat
        ChatRoomReq req = new ChatRoomReq();
        req.setType(ChatRoomType.DIRECT);
        req.setParticipantUserIds(List.of(otherUserId));
        req.setName(otherUser.getUserName()); // Set name as other user's username

        return createChatRoom(req);
    }

    private ChatParticipant createParticipant(Long chatRoomId, Long userId) {
        return ChatParticipant.builder()
                .chatRoomId(chatRoomId)
                .userId(userId)
                .joinedAt(OffsetDateTime.now())
                .isActive(true)
                .build();
    }

    private ChatRoomRes getChatRoomWithDetails(ChatRoom chatRoom) {
        ChatRoomRes res = chatRoomMapper.toDTO(chatRoom);
        
        // Get participants
        List<ChatParticipant> participants = chatParticipantRepository.findByChatRoomIdAndIsActiveTrue(chatRoom.getId());
        res.setParticipants(participants.stream()
                .map(chatParticipantMapper::toDTO)
                .collect(Collectors.toList()));
        
        // Get last message
        Page<ChatMessage> lastMessages = chatMessageRepository.findLastMessageByChatRoomId(chatRoom.getId(), Pageable.ofSize(1));


        if(lastMessages.getSize() > 0) {
            res.setLastMessage(chatMessageMapper.toDTO(lastMessages.getContent().get(0)));
        }
        
        // Get unread count for current user
        GUser currentUser = getCurrentUser();
        Long unreadCount = chatMessageRepository.countUnreadMessages(
                chatRoom.getId(),
                currentUser.getId(),
                OffsetDateTime.now().minusYears(10) // Default date for users who never read
        );
        res.setUnreadCount(unreadCount);

        return res;
    }

    @Override
    public ChatMessageRes sendMessage(ChatMessageReq req) {
        GUser currentUser = getCurrentUser();

        // Validate chat room exists and user is participant
        ChatRoom chatRoom = chatRoomRepository.findById(req.getChatRoomId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.INVALID_REQUEST));

        chatParticipantRepository.findByChatRoomIdAndUserIdAndIsActiveTrue(req.getChatRoomId(), currentUser.getId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.PERMISSION_DENIED));
        
        // Create message
        ChatMessage message = chatMessageMapper.toEntity(req);
        message.setSenderId(currentUser.getId());
        message = chatMessageRepository.save(message);
        
        // Update chat room's updated_at
        chatRoom.setUpdatedAt(OffsetDateTime.now());
        chatRoomRepository.save(chatRoom);
        
        ChatMessageRes messageRes = chatMessageMapper.toDTO(message);
        
        // Send real-time notification to all participants
        List<Long> participantIds = chatParticipantRepository.findUserIdsByChatRoomId(req.getChatRoomId());
        for (Long participantId : participantIds) {
            if (!participantId.equals(currentUser.getId())) {
                messagingTemplate.convertAndSendToUser(
                        participantId.toString(),
                        "/queue/messages",
                        messageRes
                );
            }
        }
        
        return messageRes;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ChatMessageRes> getMessages(Long chatRoomId, Pageable pageable) {
        GUser currentUser = getCurrentUser();

        // Validate user is participant
        chatParticipantRepository.findByChatRoomIdAndUserIdAndIsActiveTrue(chatRoomId, currentUser.getId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.PERMISSION_DENIED));

        Page<ChatMessage> messages = chatMessageRepository.findMessagesByChatRoomId(chatRoomId, pageable);

        return messages.map(chatMessageMapper::toDTO);
    }

    @Override
    public void markAsRead(Long chatRoomId) {
        GUser currentUser = getCurrentUser();

        // Validate user is participant
        chatParticipantRepository.findByChatRoomIdAndUserIdAndIsActiveTrue(chatRoomId, currentUser.getId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.PERMISSION_DENIED));

        chatParticipantRepository.updateLastReadAt(chatRoomId, currentUser.getId(), OffsetDateTime.now());
    }

    @Override
    @Transactional(readOnly = true)
    public ChatRoomRes getChatRoom(Long chatRoomId) {
        GUser currentUser = getCurrentUser();

        ChatRoom chatRoom = chatRoomRepository.findById(chatRoomId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.INVALID_REQUEST));

        // Validate user is participant
        chatParticipantRepository.findByChatRoomIdAndUserIdAndIsActiveTrue(chatRoomId, currentUser.getId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.PERMISSION_DENIED));

        return getChatRoomWithDetails(chatRoom);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChatUserRes> getAvailableUsers() {
        GUser currentUser = getCurrentUser();
        List<Role> currentUserRoles = currentUser.getRoles();

        List<Role> targetRoles = new ArrayList<>();

        // If current user is PANEL, show ADMIN_PANEL users
        if (currentUserRoles.contains(Role.PANEL)) {
            targetRoles.add(Role.ADMIN_PANEL);
        }

        // If current user is ADMIN_PANEL, show PANEL users
        if (currentUserRoles.contains(Role.ADMIN_PANEL)) {
            targetRoles.add(Role.PANEL);
        }

        if (targetRoles.isEmpty()) {
            return new ArrayList<>();
        }

        // Find users with target roles
        List<GUser> users = new ArrayList<>();
        if (targetRoles.size() >= 1) {
            String role1 = targetRoles.get(0).toString();
            String role2 = targetRoles.size() > 1 ? targetRoles.get(1).toString() : null;
            users = userRepository.findByRolesContainingAny(role1, role2);
        }

        return users.stream()
                .filter(user -> !user.getId().equals(currentUser.getId()))
                .map(this::mapToChatUserRes)
                .collect(Collectors.toList());
    }

    @Override
    public void deleteChatRoom(Long chatRoomId) {
        GUser currentUser = getCurrentUser();

        ChatRoom chatRoom = chatRoomRepository.findById(chatRoomId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.INVALID_REQUEST));

        // Only creator or admin can delete chat room
        if (!chatRoom.getCreatedBy().equals(currentUser.getId()) &&
            !currentUser.getRoles().contains(Role.ADMIN_PANEL)) {
            throw new InvalidParameterException(IdErrorCode.PERMISSION_DENIED);
        }

        chatRoom.setIsActive(false);
        chatRoomRepository.save(chatRoom);
    }

    private ChatUserRes mapToChatUserRes(GUser user) {
        ChatUserRes res = new ChatUserRes();
        res.setId(user.getId());
        res.setUserName(user.getUserName());
        res.setFullName(user.getUserName()); // Use userName as fullName since getFullName() doesn't exist
        res.setEmail(user.getEmail());
        res.setAvatar(user.getAvatar());
        res.setRoles(user.getRoles());
        res.setLastLoginAt(user.getLastLoginAt());
        res.setIsOnline(isUserOnline(user)); // You can implement online status logic
        return res;
    }

    private Boolean isUserOnline(GUser user) {
        // Simple implementation - consider user online if last login was within 5 minutes
        if (user.getLastLoginAt() == null) {
            return false;
        }
        return user.getLastLoginAt().isAfter(OffsetDateTime.now().minusMinutes(5));
    }

    @Override
    @Transactional
    public ChatMessageRes sendSupportMessage(ChatMessageReq req) {
        GUser currentUser = getCurrentUser();
        log.info("Support message request from user ID: {} with roles: {}", currentUser.getId(), currentUser.getRoles());

        // Only PANEL users can send support messages
        if (!currentUser.getRoles().contains(Role.PANEL)) {
            throw new InvalidParameterException(IdErrorCode.PERMISSION_DENIED);
        }

        // Get or create support chat room for this user
        ChatRoom supportRoom = getOrCreateSupportChatRoom(currentUser.getId());

        // Create support message
        ChatMessage message = chatMessageMapper.toEntity(req);
        message.setSenderId(currentUser.getId());
        message.setChatRoomId(supportRoom.getId());
        message = chatMessageRepository.save(message);

        // Update chat room's updated_at
        supportRoom.setUpdatedAt(OffsetDateTime.now());
        chatRoomRepository.save(supportRoom);

        ChatMessageRes messageRes = chatMessageMapper.toDTO(message);

        // Send real-time notification to all ADMIN_PANEL users
        List<GUser> adminPanelUsers = userRepository.findByRolesContaining(Role.ADMIN_PANEL.toString());
        log.info("Sending support message to {} admin users", adminPanelUsers.size());

        for (GUser adminUser : adminPanelUsers) {
            log.info("Sending WebSocket message to admin user ID: {} via /queue/support-messages", adminUser.getId());
            messagingTemplate.convertAndSendToUser(
                    adminUser.getId().toString(),
                    "/queue/support-messages",
                    messageRes
            );
        }

        log.info("Support message sent successfully: {}", messageRes.getContent());

        return messageRes;
    }

    @Override
    public Page<ChatMessageRes> getSupportMessages(Pageable pageable) {
        GUser currentUser = getCurrentUser();

        // Both PANEL and ADMIN_PANEL users can view support messages
        // PANEL users see their own messages from their support chat room
        Page<ChatMessage> messages;

        if (currentUser.getRoles().contains(Role.ADMIN_PANEL)) {
            // ADMIN_PANEL users see all support messages (legacy support for old implementation)
            messages = chatMessageRepository.findByChatRoomIdAndIsDeletedFalseOrderByCreatedAtDesc(-1L, pageable);
        } else if (currentUser.getRoles().contains(Role.PANEL)) {
            // PANEL users see messages from their own support chat room
            Optional<ChatRoom> supportRoom = chatRoomRepository.findSupportChatRoomByUserId(currentUser.getId());
            if (supportRoom.isEmpty()) {
                return Page.empty();
            }
            messages = chatMessageRepository.findMessagesByChatRoomId(supportRoom.get().getId(), pageable);
        } else {
            throw new InvalidParameterException(IdErrorCode.PERMISSION_DENIED);
        }

        return messages.map(message -> {
            ChatMessageRes messageRes = chatMessageMapper.toDTO(message);
            // Manually populate sender information
            if (message.getSenderId() != null) {
                GUser sender = userRepository.findById(message.getSenderId()).orElse(null);
                if (sender != null) {
                    messageRes.setSender(gUserMapper.toApiKey(sender));
                }
            }
            return messageRes;
        });
    }

    @Override
    public Page<ChatRoomRes> getSupportChatRooms(Pageable pageable) {
        GUser currentUser = getCurrentUser();

        // Only ADMIN_PANEL users can view support chat rooms
        if (!currentUser.getRoles().contains(Role.ADMIN_PANEL)) {
            throw new InvalidParameterException(IdErrorCode.PERMISSION_DENIED);
        }

        Page<ChatRoom> chatRooms = chatRoomRepository.findSupportChatRooms(pageable);

        return chatRooms.map(room -> {
            ChatRoomRes roomRes = chatRoomMapper.toDTO(room);

            // Get the last message for this room
            Page<ChatMessage> lastMessages = chatMessageRepository.findLastMessageByChatRoomId(
                room.getId(),
                Pageable.ofSize(1)
            );

            if (!lastMessages.isEmpty()) {
                ChatMessage lastMessage = lastMessages.getContent().get(0);
                ChatMessageRes lastMessageRes = chatMessageMapper.toDTO(lastMessage);

                // Populate sender information
                if (lastMessage.getSenderId() != null) {
                    GUser sender = userRepository.findById(lastMessage.getSenderId()).orElse(null);
                    if (sender != null) {
                        lastMessageRes.setSender(gUserMapper.toApiKey(sender));
                    }
                }
                roomRes.setLastMessage(lastMessageRes);
            }

            // Get creator information
            if (room.getCreatedBy() != null) {
                GUser creator = userRepository.findById(room.getCreatedBy()).orElse(null);
                if (creator != null) {
                    roomRes.setCreator(gUserMapper.toApiKey(creator));
                }
            }

            return roomRes;
        });
    }

    @Override
    public Page<ChatMessageRes> getSupportChatMessages(Long userId, Pageable pageable) {
        GUser currentUser = getCurrentUser();

        // Only ADMIN_PANEL users can view support chat messages
        if (!currentUser.getRoles().contains(Role.ADMIN_PANEL)) {
            throw new InvalidParameterException(IdErrorCode.PERMISSION_DENIED);
        }

        // Find the support chat room for this user
        Optional<ChatRoom> supportRoom = chatRoomRepository.findSupportChatRoomByUserId(userId);
        if (supportRoom.isEmpty()) {
            return Page.empty();
        }

        // Ensure current admin user is a participant in this chat room
        ensureAdminParticipant(supportRoom.get().getId(), currentUser.getId());

        Page<ChatMessage> messages = chatMessageRepository.findMessagesByChatRoomId(
            supportRoom.get().getId(),
            pageable
        );

        return messages.map(message -> {
            ChatMessageRes messageRes = chatMessageMapper.toDTO(message);
            // Populate sender information
            if (message.getSenderId() != null) {
                GUser sender = userRepository.findById(message.getSenderId()).orElse(null);
                if (sender != null) {
                    messageRes.setSender(gUserMapper.toApiKey(sender));
                }
            }
            return messageRes;
        });
    }

    @Override
    @Transactional
    public ChatMessageRes sendSupportReply(Long userId, ChatMessageReq req) {
        GUser currentUser = getCurrentUser();

        // Only ADMIN_PANEL users can send support replies
        if (!currentUser.getRoles().contains(Role.ADMIN_PANEL)) {
            throw new InvalidParameterException(IdErrorCode.PERMISSION_DENIED);
        }

        // Find the support chat room for this user
        Optional<ChatRoom> supportRoom = chatRoomRepository.findSupportChatRoomByUserId(userId);
        if (supportRoom.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.INVALID_REQUEST);
        }

        // Ensure current admin user is a participant in this chat room
        ensureAdminParticipant(supportRoom.get().getId(), currentUser.getId());

        // Create reply message
        ChatMessage message = chatMessageMapper.toEntity(req);
        message.setSenderId(currentUser.getId());
        message.setChatRoomId(supportRoom.get().getId());
        message = chatMessageRepository.save(message);

        // Update chat room's updated_at
        ChatRoom room = supportRoom.get();
        room.setUpdatedAt(OffsetDateTime.now());
        chatRoomRepository.save(room);

        ChatMessageRes messageRes = chatMessageMapper.toDTO(message);

        // Send real-time notification to the panel user
        log.info("Sending admin reply to panel user ID: {} via /queue/support-messages", userId);
        messagingTemplate.convertAndSendToUser(
                userId.toString(),
                "/queue/support-messages",
                messageRes
        );
        log.info("Admin reply sent successfully: {}", messageRes.getContent());

        return messageRes;
    }

    @Override
    public ChatRoomRes getOrCreateSupportChatRoom() {
        GUser currentUser = getCurrentUser();

        // Only PANEL users can get their support chat room
        if (!currentUser.getRoles().contains(Role.PANEL)) {
            throw new InvalidParameterException(IdErrorCode.PERMISSION_DENIED);
        }

        ChatRoom supportRoom = getOrCreateSupportChatRoom(currentUser.getId());
        return getChatRoomWithDetails(supportRoom);
    }

    /**
     * Get or create support chat room for a specific user
     */
    @Transactional
    public ChatRoom getOrCreateSupportChatRoom(Long userId) {
        // Check if support chat room already exists for this user
        Optional<ChatRoom> existingRoom = chatRoomRepository.findSupportChatRoomByUserId(userId);
        if (existingRoom.isPresent()) {
            return existingRoom.get();
        }

        // Create new support chat room for this user
        GUser user = userRepository.findById(userId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        ChatRoom supportRoom = ChatRoom.builder()
                .name("Support Chat - " + user.getUserName())
                .type(ChatRoomType.SUPPORT)
                .createdBy(userId)
                .isActive(true)
                .build();

        supportRoom = chatRoomRepository.save(supportRoom);

        // Add participants to the support chat room
        List<ChatParticipant> participants = new ArrayList<>();

        // Add the panel user as participant
        participants.add(createParticipant(supportRoom.getId(), userId));

        // Add all ADMIN_PANEL users as participants
        List<GUser> adminPanelUsers = userRepository.findByRolesContaining(Role.ADMIN_PANEL.toString());
        for (GUser adminUser : adminPanelUsers) {
            participants.add(createParticipant(supportRoom.getId(), adminUser.getId()));
        }

        chatParticipantRepository.saveAll(participants);

        return supportRoom;
    }

    /**
     * Ensure admin user is a participant in the support chat room
     */
    @Transactional
    private void ensureAdminParticipant(Long chatRoomId, Long adminUserId) {
        // Check if admin user is already a participant
        Optional<ChatParticipant> existingParticipant = chatParticipantRepository
                .findByChatRoomIdAndUserIdAndIsActiveTrue(chatRoomId, adminUserId);

        if (existingParticipant.isEmpty()) {
            // Add admin user as participant
            ChatParticipant participant = createParticipant(chatRoomId, adminUserId);
            chatParticipantRepository.save(participant);
        }
    }

    @Override
    @Transactional
    public void addAdminToAllSupportChatRooms(Long adminUserId) {
        // Validate that the user is an admin
        GUser adminUser = userRepository.findById(adminUserId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        if (!adminUser.getRoles().contains(Role.ADMIN_PANEL)) {
            throw new InvalidParameterException(IdErrorCode.PERMISSION_DENIED);
        }

        // Get all support chat rooms
        List<ChatRoom> supportRooms = chatRoomRepository.findSupportChatRooms(Pageable.unpaged()).getContent();

        List<ChatParticipant> newParticipants = new ArrayList<>();

        for (ChatRoom room : supportRooms) {
            // Check if admin is already a participant
            Optional<ChatParticipant> existingParticipant = chatParticipantRepository
                    .findByChatRoomIdAndUserIdAndIsActiveTrue(room.getId(), adminUserId);

            if (existingParticipant.isEmpty()) {
                // Add admin as participant
                newParticipants.add(createParticipant(room.getId(), adminUserId));
            }
        }

        if (!newParticipants.isEmpty()) {
            chatParticipantRepository.saveAll(newParticipants);
            log.info("Added admin user {} to {} support chat rooms", adminUserId, newParticipants.size());
        }
    }

    /**
     * Ensure support chat room exists, create if not
     */
    @Transactional
    public void ensureSupportChatRoomExists() {
        try {
            Optional<ChatRoom> existingRoom = chatRoomRepository.findById(-1L);
            if (existingRoom.isEmpty()) {
                // Create support chat room using native SQL to set specific ID
                chatRoomRepository.createSupportChatRoom();
            }
        } catch (Exception e) {
            // If room creation fails, it might already exist due to concurrent access
            // Just log and continue
            log.warn("Failed to create support chat room, it might already exist: {}", e.getMessage());
        }
    }
}
