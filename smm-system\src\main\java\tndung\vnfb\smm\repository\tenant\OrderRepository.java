package tndung.vnfb.smm.repository.tenant;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tndung.vnfb.smm.constant.enums.OrderStatus;
import tndung.vnfb.smm.entity.GOrder;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

public interface OrderRepository extends TenantAwareRepository<GOrder, Long> {
    @Query("SELECT o FROM GOrder o WHERE o.id IN :orderIds AND o.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} AND o.isDeleted = false")
    List<GOrder> findByIdIn(@Param("orderIds") List<Long> orderIds);

    @Query("SELECT o FROM GOrder o WHERE o.id = :orderId AND o.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} AND o.isDeleted = false")
    Optional<GOrder> findById(@Param("orderId") Long orderId);

    @Query("select o from  GOrder o " +
            "inner join GUser u on o.user.id = u.id " +
            " where o.status in :statuses AND o.isDeleted = false")
    Page<GOrder> findOrderToReady(List<OrderStatus> statuses, Pageable pageable);

    @Query("SELECT COUNT(o) FROM GOrder o WHERE o.createdAt BETWEEN :startDate AND :endDate " +
            "AND o.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} AND o.isDeleted = false")
    Long countOrdersBetweenDates(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate);



    @Query("SELECT o FROM GOrder o WHERE " +
            "(:link IS NULL OR o.link = :link) " +
            "AND (:orderId IS NULL OR o.id = :orderId) " +
            "AND (:apiProviderId IS NULL OR o.apiProvider.id = :apiProviderId) " +
            "AND (:categoryId IS NULL OR o.service.category.id = :categoryId) " +
            "AND (:serviceId IS NULL OR o.service.id = :serviceId) " +
            "AND (CAST(:startDate AS timestamp)  IS NULL OR o.createdAt >= :startDate) " +
            "AND (CAST(:endDate AS timestamp) IS NULL OR o.createdAt <= :endDate) " +
            "AND (:status IS NULL OR o.status = :status)" +

            "AND (:userId IS NULL OR o.user.id = :userId)" +
            " AND o.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} "+
            " AND o.isDeleted = false" +
            " order by o.createdAt desc")
    Page<GOrder> searchOrders(
            @Param("link") String link,
            @Param("orderId") Long orderId,
            @Param("apiProviderId") Long apiProviderId,
            @Param("categoryId") Long categoryId,
            @Param("serviceId") Long serviceId,
            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate,
            @Param("status") OrderStatus status,
            @Param("userId") Long userId,
            Pageable pageable);

    @Query("SELECT o FROM GOrder o WHERE " +
            " o.user.id = :userId " +
            "AND (:link IS NULL OR o.link = :link) " +
            "AND (:orderId IS NULL OR o.id = :orderId) " +
            "AND (:categoryId IS NULL OR o.service.category.id = :categoryId) " +
            "AND (:serviceId IS NULL OR o.service.id = :serviceId) " +
            "AND (CAST(:startDate AS timestamp) IS NULL OR o.createdAt >= :startDate) " +
            "AND (CAST(:endDate AS timestamp) IS NULL OR o.createdAt <= :endDate)" +
            "AND (:status IS NULL OR o.status = :status)" +
            " AND o.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} "+
            " AND o.isDeleted = false" +
            " order by o.createdAt desc")
    Page<GOrder> searchMyOrders(
            @Param("userId") Long userId,
            @Param("link") String link,
            @Param("orderId") Long orderId,
            @Param("serviceId") Long serviceId,
            @Param("categoryId") Long categoryId,

            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate,
            @Param("status") OrderStatus status,
            Pageable pageable);
    /**
     * Count orders for a specific tenant ID
     */
    @Query("SELECT COUNT(o) FROM GOrder o WHERE o.tenantId = :tenantId AND o.isDeleted = false")
    Long countByTenantId(@Param("tenantId") String tenantId);





}
